/**
 * Test Supabase Connection
 * Simple test to check if we can connect to Supabase
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function testConnection() {
  console.log('🔍 Testing Supabase connection...')
  console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
  console.log('Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Present' : 'Missing')
  
  try {
    // Test 1: Simple query
    console.log('\n📋 Test 1: Basic connection test')
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ Error:', error.message)
      console.log('Code:', error.code)
      console.log('Details:', error.details)
      
      if (error.message.includes('relation "appearance_settings" does not exist')) {
        console.log('\n💡 Solution: The table does not exist. You need to run the SQL script.')
        console.log('📝 Go to: https://supabase.com/dashboard/project/iidvfmruzoyrfboptggm/sql')
        console.log('📋 Copy and paste the SQL from database/fix_appearance_settings.sql')
        console.log('▶️ Click Run')
      }
    } else {
      console.log('✅ Connection successful!')
      console.log('Data:', data)
    }
    
    // Test 2: Check if we can create a simple table
    console.log('\n📋 Test 2: Permission test')
    const { error: createError } = await supabase.rpc('version')
    
    if (createError) {
      console.log('❌ Permission error:', createError.message)
    } else {
      console.log('✅ Permissions OK')
    }
    
  } catch (err) {
    console.log('❌ Connection failed:', err.message)
  }
}

testConnection()
