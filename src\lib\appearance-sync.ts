/**
 * Real-time Appearance Settings Synchronization
 * Ensures consistent background settings across all application instances
 */

import type { AppearanceSettings } from './appearance-service'
import { logger } from './logger'
import { supabase } from './supabase'

export class AppearanceSync {
  private static listeners: Set<(settings: AppearanceSettings) => void> = new Set()
  private static subscription: any = null
  private static isInitialized = false
  private static retryCount = 0
  private static maxRetries = 3
  private static retryTimeout: NodeJS.Timeout | null = null
  private static pollingInterval: NodeJS.Timeout | null = null
  private static lastKnownSettings: AppearanceSettings | null = null
  private static isPollingMode = false

  /**
   * Initialize real-time synchronization
   */
  static initialize() {
    if (this.isInitialized) {
      logger.info('🔄 Appearance sync already initialized')
      return
    }

    // Clean up any existing subscriptions first
    this.cleanup()

    // Check if polling is disabled or if we should skip initialization
    if (typeof window !== 'undefined' && window.localStorage?.getItem('disable-appearance-polling') === 'true') {
      logger.info('🔄 Appearance sync disabled via localStorage')
      this.isInitialized = true // Mark as initialized but don't start polling
      return
    }

    // Skip real-time and use polling mode for stability
    logger.info('🔄 Initializing appearance settings sync (polling mode)')
    this.startPollingMode()
  }



  /**
   * Start polling mode as fallback when real-time fails
   */
  private static startPollingMode() {
    if (this.isPollingMode) {
      return // Already in polling mode
    }

    // Check if polling is disabled via environment variable
    if (typeof window !== 'undefined' && window.localStorage?.getItem('disable-appearance-polling') === 'true') {
      logger.info('🔄 Appearance polling disabled via localStorage')
      return
    }

    this.isPollingMode = true
    this.isInitialized = true
    logger.info('🔄 Starting polling mode for appearance sync (every 10 seconds)')

    // Initial sync with delay to avoid immediate errors
    setTimeout(() => {
      this.pollForChanges()
    }, 2000)

    // Set up polling interval (increased to 10 seconds to reduce load)
    this.pollingInterval = setInterval(() => {
      this.pollForChanges()
    }, 10000) // Poll every 10 seconds
  }

  /**
   * Poll for changes in polling mode
   */
  private static async pollForChanges() {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') {
        return
      }

      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value, updated_at')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No rows found - table might be empty, create default settings
          logger.info('🔄 No appearance settings found, using defaults')
          return
        }

        if (error.message?.includes('relation "appearance_settings" does not exist')) {
          // Table doesn't exist
          logger.warn('⚠️ appearance_settings table does not exist. Please run database migrations.')
          this.stopPollingMode() // Stop polling if table doesn't exist
          return
        }

        // Log other errors but don't spam the console
        if (this.retryCount < 3) {
          logger.error('❌ Polling failed', {
            error: error.message,
            code: error.code,
            retryCount: this.retryCount
          })
          this.retryCount++
        }
        return
      }

      // Reset retry count on success
      this.retryCount = 0

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings

        // Check if settings have changed
        if (!this.lastKnownSettings ||
            JSON.stringify(this.lastKnownSettings) !== JSON.stringify(settings)) {

          logger.info('🔄 Polling detected settings change', {
            backgroundType: settings.backgroundType,
            hasImage: !!settings.backgroundImage
          })

          this.lastKnownSettings = settings
          this.broadcastChange(settings)
        }
      }
    } catch (error) {
      // Handle network errors and other exceptions
      if (this.retryCount < 3) {
        logger.error('❌ Error during polling', {
          error: error instanceof Error ? error.message : 'Unknown error',
          retryCount: this.retryCount
        })
        this.retryCount++
      }

      // If too many errors, stop polling temporarily
      if (this.retryCount >= 3) {
        logger.warn('⚠️ Too many polling errors, stopping polling for 30 seconds')
        this.stopPollingMode()

        // Restart polling after 30 seconds
        setTimeout(() => {
          this.retryCount = 0
          this.startPollingMode()
        }, 30000)
      }
    }
  }

  /**
   * Stop polling mode
   */
  private static stopPollingMode() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
    this.isPollingMode = false
    logger.info('🛑 Polling mode stopped')
  }

  /**
   * Cleanup subscriptions
   */
  static cleanup() {
    // Clear retry timeout
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
      this.retryTimeout = null
    }

    // Stop polling mode
    this.stopPollingMode()

    // Remove subscription
    if (this.subscription) {
      supabase.removeChannel(this.subscription)
      this.subscription = null
    }

    // Remove event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('appearance-settings-updated', this.handleCustomEvent.bind(this) as EventListener)
    }

    // Reset state
    this.listeners.clear()
    this.isInitialized = false
    this.retryCount = 0
    this.lastKnownSettings = null

    logger.info('🧹 Appearance sync cleanup completed')
  }

  /**
   * Add a listener for settings changes
   */
  static addListener(callback: (settings: AppearanceSettings) => void) {
    this.listeners.add(callback)
    logger.info(`📡 Added appearance settings listener (${this.listeners.size} total)`)

    // Return cleanup function
    return () => {
      this.listeners.delete(callback)
      logger.info(`📡 Removed appearance settings listener (${this.listeners.size} remaining)`)
    }
  }

  /**
   * Broadcast settings change to all listeners
   */
  static broadcastChange(settings: AppearanceSettings) {
    logger.info('📢 Broadcasting appearance settings change', { 
      backgroundType: settings.backgroundType,
      hasImage: !!settings.backgroundImage,
      listenerCount: this.listeners.size
    })

    // Notify all listeners
    this.listeners.forEach(callback => {
      try {
        callback(settings)
      } catch (error) {
        logger.error('Error in appearance settings listener', { error })
      }
    })

    // Broadcast to same-tab components only (no localStorage)
    if (typeof window !== 'undefined') {
      // Dispatch custom event for same-tab communication
      window.dispatchEvent(new CustomEvent('appearance-settings-updated', {
        detail: settings
      }))
    }
  }

  /**
   * Disable polling mode (for troubleshooting)
   */
  static disablePolling() {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('disable-appearance-polling', 'true')
    }
    this.stopPollingMode()
    logger.info('🛑 Appearance polling disabled')
  }

  /**
   * Enable polling mode
   */
  static enablePolling() {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('disable-appearance-polling')
    }
    this.startPollingMode()
    logger.info('🔄 Appearance polling enabled')
  }

  /**
   * Get connection status for debugging
   */
  static getConnectionStatus() {
    return {
      isInitialized: this.isInitialized,
      hasSubscription: !!this.subscription,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      hasRetryTimeout: !!this.retryTimeout,
      isPollingMode: this.isPollingMode,
      hasPollingInterval: !!this.pollingInterval,
      listenerCount: this.listeners.size,
      hasLastKnownSettings: !!this.lastKnownSettings,
      isPollingDisabled: typeof window !== 'undefined' ? window.localStorage?.getItem('disable-appearance-polling') === 'true' : false
    }
  }

  /**
   * Force sync from database
   */
  static async forceSyncFromDatabase(): Promise<AppearanceSettings | null> {
    try {
      logger.info('🔄 Force syncing appearance settings from database')

      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) {
        logger.error('Failed to force sync from database', { error })
        return null
      }

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings
        logger.info('✅ Force sync successful', { 
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })
        
        this.broadcastChange(settings)
        return settings
      }

      return null
    } catch (error) {
      logger.error('Error during force sync', { error })
      return null
    }
  }





  /**
   * Handle custom events (same-tab communication)
   */
  private static handleCustomEvent(event: CustomEvent) {
    if (event.detail) {
      logger.info('🔄 Same-tab settings change detected')
      // Don't re-broadcast to avoid loops
      this.listeners.forEach(callback => {
        try {
          callback(event.detail)
        } catch (error) {
          logger.error('Error in custom event listener', { error })
        }
      })
    }
  }
}

// DISABLED: Auto-initialization disabled to prevent real-time errors
// Manual initialization only when needed
if (typeof window !== 'undefined') {
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    AppearanceSync.cleanup()
  })
}
