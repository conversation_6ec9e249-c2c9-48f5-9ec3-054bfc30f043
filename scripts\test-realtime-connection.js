/**
 * Test Real-time Connection for Appearance Sync
 * This script helps diagnose real-time subscription issues
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  {
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  }
)

async function testRealtimeConnection() {
  console.log('🔧 Testing Real-time Connection...\n')

  try {
    // Step 1: Test basic database connection
    console.log('1️⃣ Testing basic database connection...')
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (error && error.code !== 'PGRST116') {
      console.log('❌ Database connection failed:', error.message)
      return false
    }

    console.log('✅ Database connection successful')
    if (data) {
      console.log('   Current settings:', {
        backgroundType: data.setting_value?.backgroundType,
        hasImage: !!data.setting_value?.backgroundImage
      })
    }

    // Step 2: Test real-time subscription
    console.log('\n2️⃣ Testing real-time subscription...')
    
    let subscriptionStatus = 'PENDING'
    let receivedChange = false
    
    const subscription = supabase
      .channel('test_appearance_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'appearance_settings',
          filter: 'setting_key=eq.landing_page_background'
        },
        (payload) => {
          console.log('✅ Real-time change received:', {
            event: payload.eventType,
            timestamp: new Date().toISOString(),
            hasNewData: !!payload.new
          })
          receivedChange = true
        }
      )
      .subscribe((status, err) => {
        subscriptionStatus = status
        console.log(`🔄 Subscription status: ${status}`, err ? { error: err } : '')
        
        if (status === 'CHANNEL_ERROR') {
          console.log('❌ Channel error details:', err)
        }
      })

    // Wait for subscription to initialize
    console.log('   Waiting for subscription to initialize...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    if (subscriptionStatus !== 'SUBSCRIBED') {
      console.log(`❌ Subscription failed with status: ${subscriptionStatus}`)
      supabase.removeChannel(subscription)
      return false
    }

    console.log('✅ Real-time subscription established')

    // Step 3: Test triggering a change
    console.log('\n3️⃣ Testing change detection...')
    
    const testSettings = data?.setting_value || {
      backgroundType: 'gradient',
      gradientStyle: 'hero-gradient',
      customColors: { primary: '#22c55e', secondary: '#facc15' },
      backgroundImage: null,
      backgroundImagePublicId: null
    }

    // Add a test timestamp to trigger a change
    const updatedSettings = {
      ...testSettings,
      testTimestamp: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('appearance_settings')
      .update({
        setting_value: updatedSettings
      })
      .eq('setting_key', 'landing_page_background')

    if (updateError) {
      console.log('❌ Failed to trigger test change:', updateError.message)
      supabase.removeChannel(subscription)
      return false
    }

    console.log('✅ Test change triggered, waiting for real-time notification...')

    // Wait for real-time notification
    await new Promise(resolve => setTimeout(resolve, 5000))

    if (receivedChange) {
      console.log('✅ Real-time change detection working!')
    } else {
      console.log('❌ Real-time change not detected')
    }

    // Cleanup
    supabase.removeChannel(subscription)

    console.log('\n🎉 Real-time connection test completed!')
    console.log('\n📋 Results:')
    console.log(`   Database Connection: ✅`)
    console.log(`   Real-time Subscription: ${subscriptionStatus === 'SUBSCRIBED' ? '✅' : '❌'}`)
    console.log(`   Change Detection: ${receivedChange ? '✅' : '❌'}`)

    return subscriptionStatus === 'SUBSCRIBED' && receivedChange

  } catch (error) {
    console.log('❌ Test failed with error:', error.message)
    return false
  }
}

// Run the test
testRealtimeConnection().then(success => {
  if (success) {
    console.log('\n✅ All real-time tests passed!')
  } else {
    console.log('\n❌ Some real-time tests failed. Check the logs above for details.')
    console.log('\n🔧 Troubleshooting tips:')
    console.log('   1. Check your internet connection')
    console.log('   2. Verify Supabase project is active')
    console.log('   3. Check if real-time is enabled in Supabase dashboard')
    console.log('   4. Try refreshing your browser')
  }
  process.exit(success ? 0 : 1)
})
