
<!DOCTYPE html>
<html>
<head>
    <title>Disable Appearance Polling</title>
</head>
<body>
    <h1>Disabling Appearance Polling</h1>
    <p>This will disable the appearance settings polling that's causing errors.</p>
    
    <script>
        // Disable polling
        localStorage.setItem('disable-appearance-polling', 'true');
        
        // Show status
        document.body.innerHTML += '<p style="color: green;">✅ Appearance polling disabled!</p>';
        document.body.innerHTML += '<p>You can now close this tab and refresh your application.</p>';
        document.body.innerHTML += '<p>To re-enable later, run: localStorage.removeItem("disable-appearance-polling")</p>';
        
        console.log('✅ Appearance polling disabled');
    </script>
</body>
</html>
