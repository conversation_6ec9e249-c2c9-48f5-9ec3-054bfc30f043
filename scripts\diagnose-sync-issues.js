/**
 * Diagnose Appearance Sync Issues
 * This script helps identify and fix real-time sync problems
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  {
    realtime: {
      params: {
        eventsPerSecond: 10
      },
      heartbeatIntervalMs: 30000,
      reconnectAfterMs: (tries) => Math.min(tries * 1000, 10000)
    }
  }
)

async function diagnoseSyncIssues() {
  console.log('🔍 Diagnosing Appearance Sync Issues...\n')

  try {
    // Step 1: Check database connectivity
    console.log('1️⃣ Checking database connectivity...')
    const { data: dbTest, error: dbError } = await supabase
      .from('appearance_settings')
      .select('count')
      .limit(1)

    if (dbError) {
      console.log('❌ Database connection failed:', dbError.message)
      return false
    }
    console.log('✅ Database connection working')

    // Step 2: Check current appearance settings
    console.log('\n2️⃣ Checking current appearance settings...')
    const { data: currentSettings, error: settingsError } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
      .single()

    if (settingsError && settingsError.code !== 'PGRST116') {
      console.log('❌ Failed to fetch settings:', settingsError.message)
      return false
    }

    if (currentSettings) {
      console.log('✅ Current settings found:', {
        backgroundType: currentSettings.setting_value?.backgroundType,
        hasImage: !!currentSettings.setting_value?.backgroundImage,
        lastUpdated: currentSettings.updated_at
      })
    } else {
      console.log('⚠️ No appearance settings found in database')
    }

    // Step 3: Test real-time subscription with detailed logging
    console.log('\n3️⃣ Testing real-time subscription...')
    
    let subscriptionEvents = []
    let changeDetected = false
    
    const subscription = supabase
      .channel('diagnostic_appearance_sync')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'appearance_settings',
          filter: 'setting_key=eq.landing_page_background'
        },
        (payload) => {
          console.log('📡 Real-time event received:', {
            event: payload.eventType,
            timestamp: new Date().toISOString(),
            hasNewData: !!payload.new,
            hasOldData: !!payload.old
          })
          changeDetected = true
        }
      )
      .subscribe((status, err) => {
        const event = {
          status,
          error: err,
          timestamp: new Date().toISOString()
        }
        subscriptionEvents.push(event)
        console.log(`🔄 Subscription event: ${status}`, err ? { error: err } : '')
      })

    // Wait for subscription to establish
    console.log('   Waiting for subscription to establish...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    // Check subscription status
    const lastEvent = subscriptionEvents[subscriptionEvents.length - 1]
    if (lastEvent?.status !== 'SUBSCRIBED') {
      console.log('❌ Subscription failed to establish')
      console.log('   Events:', subscriptionEvents)
      supabase.removeChannel(subscription)
      return false
    }

    console.log('✅ Real-time subscription established')

    // Step 4: Test change detection
    console.log('\n4️⃣ Testing change detection...')
    
    const testSettings = currentSettings?.setting_value || {
      backgroundType: 'gradient',
      gradientStyle: 'hero-gradient',
      customColors: { primary: '#22c55e', secondary: '#facc15' },
      backgroundImage: null,
      backgroundImagePublicId: null
    }

    // Trigger a change
    const updatedSettings = {
      ...testSettings,
      diagnosticTimestamp: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('appearance_settings')
      .update({ setting_value: updatedSettings })
      .eq('setting_key', 'landing_page_background')

    if (updateError) {
      console.log('❌ Failed to trigger test change:', updateError.message)
    } else {
      console.log('✅ Test change triggered')
    }

    // Wait for real-time notification
    console.log('   Waiting for real-time notification...')
    await new Promise(resolve => setTimeout(resolve, 8000))

    // Cleanup
    supabase.removeChannel(subscription)

    // Step 5: Results and recommendations
    console.log('\n📋 Diagnostic Results:')
    console.log(`   Database Connection: ✅`)
    console.log(`   Settings Found: ${currentSettings ? '✅' : '⚠️'}`)
    console.log(`   Real-time Subscription: ${lastEvent?.status === 'SUBSCRIBED' ? '✅' : '❌'}`)
    console.log(`   Change Detection: ${changeDetected ? '✅' : '❌'}`)

    console.log('\n🔧 Recommendations:')
    
    if (!changeDetected) {
      console.log('   ❌ Real-time sync is not working properly')
      console.log('   📝 The application will automatically fall back to polling mode')
      console.log('   🔄 Polling will check for changes every 5 seconds')
      console.log('   ⚡ This ensures sync still works, just with slight delay')
    } else {
      console.log('   ✅ Real-time sync is working correctly')
      console.log('   🎉 No action needed - sync should work perfectly')
    }

    console.log('\n💡 Additional Tips:')
    console.log('   • Refresh your browser to reset the sync connection')
    console.log('   • Check browser console for detailed sync logs')
    console.log('   • The app automatically handles connection issues')
    console.log('   • Both real-time and polling modes ensure reliable sync')

    return changeDetected

  } catch (error) {
    console.log('❌ Diagnostic failed:', error.message)
    return false
  }
}

// Run the diagnostic
diagnoseSyncIssues().then(success => {
  console.log('\n' + '='.repeat(60))
  if (success) {
    console.log('🎉 Real-time sync is working correctly!')
  } else {
    console.log('⚠️  Real-time sync has issues, but polling fallback will work')
  }
  console.log('='.repeat(60))
  process.exit(0) // Always exit successfully since polling fallback exists
})
