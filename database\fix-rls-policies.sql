-- =====================================================
-- FIX RLS POLICIES FOR APPEARANCE SETTINGS
-- =====================================================
-- This script fixes Row Level Security policies that are blocking access

-- =====================================================
-- 1. DISABLE RLS TEMPORARILY FOR TESTING
-- =====================================================

-- Disable RLS to allow all operations
ALTER TABLE appearance_settings DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 2. VERIFY TABLE ACCESS
-- =====================================================

-- Test basic operations
SELECT 'Testing SELECT' as test_type, COUNT(*) as record_count 
FROM appearance_settings;

-- Test specific query
SELECT 'Testing specific query' as test_type, setting_key, setting_value
FROM appearance_settings 
WHERE setting_key = 'landing_page_background';

-- =====================================================
-- 3. RE-ENABLE RLS WITH PROPER POLICIES
-- =====================================================

-- Re-enable RLS
ALTER TABLE appearance_settings ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Enable all operations for application" ON appearance_settings;
DROP POLICY IF EXISTS "Enable read access for all users" ON appearance_settings;
DROP POLICY IF EXISTS "Enable insert access for all users" ON appearance_settings;
DROP POLICY IF EXISTS "Enable update access for all users" ON appearance_settings;
DROP POLICY IF EXISTS "Enable delete access for all users" ON appearance_settings;
DROP POLICY IF EXISTS "Enable write access for all users" ON appearance_settings;

-- Create simple, permissive policies
CREATE POLICY "Allow all operations" 
    ON appearance_settings 
    FOR ALL 
    USING (true) 
    WITH CHECK (true);

-- =====================================================
-- 4. VERIFY POLICIES WORK
-- =====================================================

-- Test SELECT with RLS enabled
SELECT 'Testing SELECT with RLS' as test_type, COUNT(*) as record_count 
FROM appearance_settings;

-- Test UPDATE with RLS enabled
UPDATE appearance_settings 
SET updated_at = NOW() 
WHERE setting_key = 'landing_page_background';

-- Verify update worked
SELECT 'Testing UPDATE result' as test_type, updated_at
FROM appearance_settings 
WHERE setting_key = 'landing_page_background';

-- =====================================================
-- 5. FINAL VERIFICATION
-- =====================================================

-- Show current policies
SELECT 
    'Current Policies' as check_type,
    policyname,
    cmd,
    permissive,
    roles
FROM pg_policies 
WHERE tablename = 'appearance_settings';

-- Show RLS status
SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'appearance_settings';

SELECT '🎉 RLS POLICIES FIXED!' as final_status;
