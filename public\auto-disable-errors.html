<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Disable Database Errors</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f8fafc;
            color: #334155;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #059669;
            background: #ecfdf5;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .info {
            color: #0369a1;
            background: #eff6ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            margin: 20px 0;
        }
        .button {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.danger {
            background: #dc2626;
        }
        .button.danger:hover {
            background: #b91c1c;
        }
        .status {
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .enabled {
            background: #fef3c7;
            color: #92400e;
        }
        .disabled {
            background: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Database Error Fix Tool</h1>
        <p>This tool helps you disable database operations that are causing console errors.</p>
        
        <div class="info">
            <strong>Current Status:</strong>
            <div id="status" class="status">Checking...</div>
        </div>

        <div class="success" id="result" style="display: none;">
            ✅ Database operations have been disabled! The console errors should stop now.
        </div>

        <h3>Actions:</h3>
        <button class="button danger" onclick="disableDatabase()">
            🛑 Disable Database Operations
        </button>
        
        <button class="button" onclick="enableDatabase()">
            🔄 Enable Database Operations
        </button>

        <button class="button" onclick="refreshPage()">
            🔄 Refresh Main App
        </button>

        <div class="info">
            <h4>What this does:</h4>
            <ul>
                <li><strong>Disable:</strong> Stops all database queries that cause errors</li>
                <li><strong>Enable:</strong> Re-enables database operations (only do this after fixing the database)</li>
                <li><strong>Refresh:</strong> Reloads your main application to apply changes</li>
            </ul>
        </div>

        <div class="info">
            <h4>To fix the database permanently:</h4>
            <ol>
                <li>Go to your <a href="https://supabase.com/dashboard" target="_blank">Supabase Dashboard</a></li>
                <li>Open the SQL Editor</li>
                <li>Run the SQL from <code>database/fix_appearance_settings.sql</code></li>
                <li>Come back here and click "Enable Database Operations"</li>
            </ol>
        </div>
    </div>

    <script>
        function checkStatus() {
            const isDisabled = localStorage.getItem('disable-appearance-polling') === 'true';
            const statusEl = document.getElementById('status');
            
            if (isDisabled) {
                statusEl.textContent = 'Database operations are DISABLED (errors should be stopped)';
                statusEl.className = 'status disabled';
            } else {
                statusEl.textContent = 'Database operations are ENABLED (may cause errors if database is not set up)';
                statusEl.className = 'status enabled';
            }
        }

        function disableDatabase() {
            localStorage.setItem('disable-appearance-polling', 'true');
            document.getElementById('result').style.display = 'block';
            checkStatus();
            console.log('✅ Database operations disabled');
        }

        function enableDatabase() {
            localStorage.removeItem('disable-appearance-polling');
            document.getElementById('result').style.display = 'none';
            checkStatus();
            console.log('🔄 Database operations enabled');
        }

        function refreshPage() {
            if (window.opener) {
                window.opener.location.reload();
                window.close();
            } else {
                window.open('/', '_blank');
            }
        }

        // Check status on load
        checkStatus();

        // Auto-disable on load if there are errors
        if (localStorage.getItem('disable-appearance-polling') !== 'true') {
            console.log('🛑 Auto-disabling database operations to prevent errors...');
            disableDatabase();
        }
    </script>
</body>
</html>
