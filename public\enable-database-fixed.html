<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enable Database Operations - Fixed</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f8fafc;
            color: #334155;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #059669;
            background: #ecfdf5;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .info {
            color: #0369a1;
            background: #eff6ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            margin: 20px 0;
        }
        .button {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.success {
            background: #059669;
        }
        .button.success:hover {
            background: #047857;
        }
        .status {
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .enabled {
            background: #dcfce7;
            color: #166534;
        }
        .disabled {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Database Fixed & Ready!</h1>
        <p>The database is working! Now let's enable database operations properly.</p>
        
        <div class="success">
            <h4>✅ Database Status:</h4>
            <ul>
                <li>✅ Supabase connection: Working</li>
                <li>✅ appearance_settings table: Exists</li>
                <li>✅ Default data: Inserted</li>
                <li>✅ Permissions: Configured</li>
            </ul>
        </div>

        <div class="info">
            <strong>Current Status:</strong>
            <div id="status" class="status">Checking...</div>
        </div>

        <h3>Actions:</h3>
        <button class="button success" onclick="enableDatabaseProperly()">
            🚀 Enable Database Operations (Fixed)
        </button>
        
        <button class="button" onclick="clearAllCache()">
            🧹 Clear All Cache
        </button>

        <button class="button" onclick="refreshApp()">
            🔄 Refresh Main App
        </button>

        <div id="result" style="display: none;" class="success">
            <h4>✅ Database Operations Enabled!</h4>
            <p>All cache cleared and database operations are now active.</p>
            <p><strong>Next:</strong> Refresh your main app to see the changes.</p>
        </div>

        <div class="info">
            <h4>What this does:</h4>
            <ul>
                <li><strong>Enable Database:</strong> Removes the disable flag</li>
                <li><strong>Clear Cache:</strong> Removes all localStorage and sessionStorage</li>
                <li><strong>Refresh App:</strong> Reloads your main application</li>
            </ul>
        </div>
    </div>

    <script>
        function checkStatus() {
            const isDisabled = localStorage.getItem('disable-appearance-polling') === 'true';
            const statusEl = document.getElementById('status');
            
            if (isDisabled) {
                statusEl.textContent = 'Database operations are DISABLED';
                statusEl.className = 'status disabled';
            } else {
                statusEl.textContent = 'Database operations are ENABLED';
                statusEl.className = 'status enabled';
            }
        }

        function enableDatabaseProperly() {
            // Remove disable flag
            localStorage.removeItem('disable-appearance-polling');
            
            // Clear any cached error states
            localStorage.removeItem('appearance-sync-errors');
            localStorage.removeItem('database-error-count');
            
            // Clear any old appearance data
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('appearance') || key.includes('background'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            document.getElementById('result').style.display = 'block';
            checkStatus();
            console.log('✅ Database operations enabled and cache cleared');
        }

        function clearAllCache() {
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear service worker cache if available
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            checkStatus();
            console.log('🧹 All cache cleared');
            alert('All cache cleared! Please refresh your main app.');
        }

        function refreshApp() {
            if (window.opener) {
                window.opener.location.reload();
                window.close();
            } else {
                window.open('/', '_blank');
            }
        }

        // Check status on load
        checkStatus();

        // Auto-enable database operations since we know it's working
        console.log('🚀 Auto-enabling database operations...');
        enableDatabaseProperly();
    </script>
</body>
</html>
