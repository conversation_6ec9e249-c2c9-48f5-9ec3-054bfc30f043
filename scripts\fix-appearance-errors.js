/**
 * Fix Appearance Settings Errors
 * This script diagnoses and fixes appearance settings related errors
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function checkTableExists() {
  console.log('🔍 Checking if appearance_settings table exists...')
  
  try {
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      if (error.message.includes('relation "appearance_settings" does not exist')) {
        console.log('❌ Table does not exist')
        return false
      }
      console.log('⚠️ Error checking table:', error.message)
      return false
    }
    
    console.log('✅ Table exists')
    return true
  } catch (err) {
    console.log('❌ Connection error:', err.message)
    return false
  }
}

async function checkTableData() {
  console.log('🔍 Checking table data...')
  
  try {
    const { data, error } = await supabase
      .from('appearance_settings')
      .select('*')
      .eq('setting_key', 'landing_page_background')
    
    if (error) {
      console.log('❌ Error reading data:', error.message)
      return false
    }
    
    if (!data || data.length === 0) {
      console.log('⚠️ No data found for landing_page_background')
      return false
    }
    
    console.log('✅ Data exists:', data[0])
    return true
  } catch (err) {
    console.log('❌ Error:', err.message)
    return false
  }
}

async function createDefaultData() {
  console.log('🔧 Creating default appearance settings...')
  
  const defaultSettings = {
    backgroundType: 'gradient',
    gradientStyle: 'hero-gradient',
    customColors: {
      primary: '#22c55e',
      secondary: '#facc15'
    },
    backgroundImage: null,
    backgroundImagePublicId: null
  }
  
  try {
    const { data, error } = await supabase
      .from('appearance_settings')
      .upsert({
        setting_key: 'landing_page_background',
        setting_value: defaultSettings
      })
      .select()
    
    if (error) {
      console.log('❌ Error creating data:', error.message)
      return false
    }
    
    console.log('✅ Default data created:', data)
    return true
  } catch (err) {
    console.log('❌ Error:', err.message)
    return false
  }
}

async function testConnection() {
  console.log('🔍 Testing Supabase connection...')

  try {
    // Test with a simple query that should always work
    const { data, error } = await supabase
      .rpc('version')

    if (error) {
      console.log('❌ Connection failed:', error.message)
      return false
    }

    console.log('✅ Connection successful')
    return true
  } catch (err) {
    console.log('❌ Connection error:', err.message)
    return false
  }
}

async function main() {
  console.log('🚀 Starting appearance settings diagnostics...\n')
  
  // Test basic connection
  const connectionOk = await testConnection()
  if (!connectionOk) {
    console.log('\n❌ Cannot connect to Supabase. Please check your environment variables.')
    process.exit(1)
  }
  
  console.log('')
  
  // Check if table exists
  const tableExists = await checkTableExists()
  if (!tableExists) {
    console.log('\n❌ appearance_settings table does not exist.')
    console.log('📝 Please run the SQL from database/fix_appearance_settings.sql in your Supabase dashboard.')
    console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql')
    process.exit(1)
  }
  
  console.log('')
  
  // Check if data exists
  const dataExists = await checkTableData()
  if (!dataExists) {
    console.log('\n🔧 Creating default data...')
    const created = await createDefaultData()
    if (!created) {
      console.log('❌ Failed to create default data')
      process.exit(1)
    }
  }
  
  console.log('\n✅ All checks passed! Appearance settings should work now.')
  console.log('\n💡 If you still see errors, you can disable polling by running:')
  console.log('   localStorage.setItem("disable-appearance-polling", "true")')
  console.log('   in your browser console.')
}

main().catch(console.error)
