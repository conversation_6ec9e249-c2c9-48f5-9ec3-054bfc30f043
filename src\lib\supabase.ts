import { createClient } from '@supabase/supabase-js'

import { config } from './env'
import { logger } from './logger'

// Create Supabase client with validated environment variables and improved reliability
export const supabase = createClient(
  config.database.url,
  config.database.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    db: {
      schema: 'public'
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      },
      heartbeatIntervalMs: 30000,
      reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 10000)
    },
    global: {
      headers: {
        'X-Client-Info': 'revantad-store@1.0.0'
      },
      fetch: (url, options = {}) => {
        // Add retry logic and better error handling for network issues
        return fetch(url, {
          ...options,
          // Reduce timeout to fail faster and retry sooner
          signal: AbortSignal.timeout(15000), // 15 second timeout instead of 30
          // Add keep-alive for better connection reuse
          keepalive: true,
          // Add additional headers for better connection handling
          headers: {
            ...options.headers,
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
          }
        })
      }
    }
  }
)

// Create server-side Supabase client with service role key for API routes
export const supabaseAdmin = createClient(
  config.database.url,
  config.database.serviceRoleKey || config.database.anonKey, // Fallback to anon key if service role not available
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    db: {
      schema: 'public'
    },
    global: {
      headers: {
        'X-Client-Info': 'revantad-store-admin@1.0.0'
      }
    }
  }
)

// Retry wrapper for Supabase operations to handle intermittent network issues
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 5, // Increased from 3 to 5
  delayMs: number = 500   // Reduced from 1000ms to 500ms for faster retries
): Promise<T> {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation()

      // If we get here, the operation succeeded
      if (attempt > 1) {
        logger.info(`Supabase operation succeeded on attempt ${attempt}/${maxRetries}`)
      }

      return result
    } catch (error) {
      lastError = error as Error

      // Expanded list of retryable errors based on the logs
      const isRetryableError =
        error instanceof Error && (
          error.message.includes('fetch failed') ||
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ENOTFOUND') ||
          error.message.includes('ETIMEDOUT') ||
          error.message.includes('TimeoutError') ||
          error.message.includes('TypeError: fetch failed') ||
          error.name === 'AbortError' ||
          error.name === 'TimeoutError'
        )

      if (!isRetryableError || attempt === maxRetries) {
        // Don't retry non-network errors or if we've exhausted retries
        throw error
      }

      logger.warn(`Supabase operation failed (attempt ${attempt}/${maxRetries}): ${error.message}`)
      logger.warn(`Retrying in ${delayMs * attempt}ms`)

      // Wait before retrying with exponential backoff (but shorter delays)
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt))
    }
  }

  // This should never be reached, but just in case
  throw lastError || new Error('Operation failed after all retries')
}

// Database Types
export interface Product {
  id: string
  name: string
  image_url?: string
  image_public_id?: string // Cloudinary public ID for automatic cleanup
  net_weight: string
  price: number // Unit/wholesale price
  retail_price?: number // Retail selling price (optional)
  stock_quantity: number
  category: string
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  customer_name: string
  customer_family_name: string
  profile_picture_url?: string | null
  profile_picture_public_id?: string | null
  phone_number?: string | null
  address?: string | null
  birth_date?: string | null
  birth_place?: string | null
  notes?: string | null
  created_at: string
  updated_at: string
}

// Customer Debt Management Interfaces
export interface CustomerDebt {
  id: string
  customer_name: string
  customer_family_name: string
  product_name: string
  product_price: number
  quantity: number
  total_amount: number
  debt_date: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerPayment {
  id: string
  customer_name: string
  customer_family_name: string
  payment_amount: number
  payment_date: string
  payment_method: string
  responsible_family_member?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerBalance {
  customer_name: string
  customer_family_name: string
  total_debt: number
  total_payments: number
  remaining_balance: number
  change_amount: number
  last_debt_date?: string
  last_payment_date?: string
  debt_count: number
  payment_count: number
  balance_status: 'No Debt' | 'Paid' | 'Outstanding' | 'Unpaid' | 'Overpaid' | 'Unknown'
  payment_percentage: number
  profile_picture_url?: string | null
  profile_picture_public_id?: string | null
}

// Payment Methods
export const PAYMENT_METHODS = [
  'Cash',
  'GCash',
  'PayMaya',
  'Bank Transfer',
  'Others'
] as const

export type PaymentMethod = typeof PAYMENT_METHODS[number]



// Product Categories
export const PRODUCT_CATEGORIES = [
  'Snacks',
  'Canned Goods',
  'Beverages',
  'Personal Care',
  'Household Items',
  'Condiments',
  'Rice & Grains',
  'Instant Foods',
  'Dairy Products',
  'Others'
] as const

export type ProductCategory = typeof PRODUCT_CATEGORIES[number]
