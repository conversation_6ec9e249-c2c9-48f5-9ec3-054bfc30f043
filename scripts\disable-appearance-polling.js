/**
 * Disable Appearance Polling
 * This script temporarily disables appearance settings polling to stop errors
 */

console.log('🛑 Disabling appearance settings polling...')

// Create a simple HTML file to run in browser
const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Disable Appearance Polling</title>
</head>
<body>
    <h1>Disabling Appearance Polling</h1>
    <p>This will disable the appearance settings polling that's causing errors.</p>
    
    <script>
        // Disable polling
        localStorage.setItem('disable-appearance-polling', 'true');
        
        // Show status
        document.body.innerHTML += '<p style="color: green;">✅ Appearance polling disabled!</p>';
        document.body.innerHTML += '<p>You can now close this tab and refresh your application.</p>';
        document.body.innerHTML += '<p>To re-enable later, run: localStorage.removeItem("disable-appearance-polling")</p>';
        
        console.log('✅ Appearance polling disabled');
    </script>
</body>
</html>
`

const fs = require('fs')
const path = require('path')

// Write the HTML file
const htmlPath = path.join(__dirname, '..', 'public', 'disable-polling.html')
fs.writeFileSync(htmlPath, htmlContent)

console.log('✅ Created disable-polling.html')
console.log('🌐 Open http://localhost:3000/disable-polling.html in your browser to disable polling')
console.log('')
console.log('Alternative: Run this in your browser console:')
console.log('localStorage.setItem("disable-appearance-polling", "true")')
